package service

import (
	"fmt"
	"log"
	"sync"
	config "test/conf"
	"time"

	"gorm.io/gorm"
)

// ConfigAwareService 配置感知的服务示例
// 展示如何在服务中使用从 Nacos 读取的配置
type ConfigAwareService struct {
	db               *gorm.DB
	adenATSConfig    *config.AdenATSConfig
	dataProcessConfig *config.DataProcessConfig
	exportConfig     *config.ExportConfig
	
	// 基于配置创建的资源
	rawChan          chan []byte
	parsedChan       chan interface{}
	deadChan         chan []byte
	workerPool       *WorkerPool
	
	// 控制
	stopChan         chan struct{}
	wg               *sync.WaitGroup
}

// WorkerPool 工作线程池
type WorkerPool struct {
	workers    int
	taskChan   chan func()
	stopChan   chan struct{}
	wg         *sync.WaitGroup
}

// NewConfigAwareService 创建配置感知的服务
func NewConfigAwareService(db *gorm.DB) (*ConfigAwareService, error) {
	// 获取各项配置
	adenATSConfig := config.GetAdenATSConfig()
	dataProcessConfig := config.GetDataProcessConfig()
	exportConfig := config.GetExportConfig()
	
	// 基于配置创建通道
	rawChan := make(chan []byte, dataProcessConfig.RawBufferSize)
	parsedChan := make(chan interface{}, dataProcessConfig.ParsedBufferSize)
	deadChan := make(chan []byte, dataProcessConfig.DeadBufferSize)
	
	// 创建工作线程池
	workerPool := &WorkerPool{
		workers:  dataProcessConfig.WorkerNum,
		taskChan: make(chan func(), dataProcessConfig.BatchSize),
		stopChan: make(chan struct{}),
		wg:       &sync.WaitGroup{},
	}
	
	service := &ConfigAwareService{
		db:                db,
		adenATSConfig:     adenATSConfig,
		dataProcessConfig: dataProcessConfig,
		exportConfig:      exportConfig,
		rawChan:           rawChan,
		parsedChan:        parsedChan,
		deadChan:          deadChan,
		workerPool:        workerPool,
		stopChan:          make(chan struct{}),
		wg:                &sync.WaitGroup{},
	}
	
	return service, nil
}

// Start 启动服务
func (s *ConfigAwareService) Start() error {
	log.Println("启动配置感知服务...")
	
	// 打印配置信息
	s.printConfigInfo()
	
	// 启动工作线程池
	s.startWorkerPool()
	
	// 启动数据处理协程
	s.startDataProcessors()
	
	// 启动清理任务
	s.startCleanupTask()
	
	log.Println("✓ 配置感知服务启动完成")
	return nil
}

// Stop 停止服务
func (s *ConfigAwareService) Stop() {
	log.Println("停止配置感知服务...")
	
	close(s.stopChan)
	close(s.workerPool.stopChan)
	
	s.wg.Wait()
	s.workerPool.wg.Wait()
	
	log.Println("✓ 配置感知服务已停止")
}

// printConfigInfo 打印配置信息
func (s *ConfigAwareService) printConfigInfo() {
	log.Println("=== 服务配置信息 ===")
	
	// 亚丁ATS配置
	log.Printf("ATS配置:")
	log.Printf("  - 基础URL: %s", s.adenATSConfig.BaseURL)
	log.Printf("  - 连接超时: %d秒", s.adenATSConfig.Timeout)
	log.Printf("  - 心跳间隔: %d毫秒", s.adenATSConfig.Heartbeat)
	
	// 数据处理配置
	log.Printf("数据处理配置:")
	log.Printf("  - 原始数据缓冲区: %d", s.dataProcessConfig.RawBufferSize)
	log.Printf("  - 工作线程数: %d", s.dataProcessConfig.WorkerNum)
	log.Printf("  - 批处理大小: %d", s.dataProcessConfig.BatchSize)
	
	// 文件导出配置
	log.Printf("文件导出配置:")
	log.Printf("  - 导出路径: %s", s.exportConfig.Path)
	log.Printf("  - 保留天数: %d", s.exportConfig.RetentionDays)
	
	log.Println("==================")
}

// startWorkerPool 启动工作线程池
func (s *ConfigAwareService) startWorkerPool() {
	log.Printf("启动 %d 个工作线程...", s.workerPool.workers)
	
	for i := 0; i < s.workerPool.workers; i++ {
		s.workerPool.wg.Add(1)
		go func(workerID int) {
			defer s.workerPool.wg.Done()
			
			log.Printf("工作线程 %d 已启动", workerID)
			
			for {
				select {
				case task := <-s.workerPool.taskChan:
					task()
				case <-s.workerPool.stopChan:
					log.Printf("工作线程 %d 已停止", workerID)
					return
				}
			}
		}(i)
	}
}

// startDataProcessors 启动数据处理协程
func (s *ConfigAwareService) startDataProcessors() {
	// 启动解析器工作线程
	for i := 0; i < s.dataProcessConfig.ParserWorkerNum; i++ {
		s.wg.Add(1)
		go s.dataParser(i)
	}
	
	// 启动数据库写入工作线程
	for i := 0; i < s.dataProcessConfig.DbWorkerNum; i++ {
		s.wg.Add(1)
		go s.dbWriter(i)
	}
	
	log.Printf("启动了 %d 个解析器和 %d 个数据库写入器", 
		s.dataProcessConfig.ParserWorkerNum, 
		s.dataProcessConfig.DbWorkerNum)
}

// dataParser 数据解析器
func (s *ConfigAwareService) dataParser(parserID int) {
	defer s.wg.Done()
	
	log.Printf("数据解析器 %d 已启动", parserID)
	
	for {
		select {
		case rawData := <-s.rawChan:
			// 模拟数据解析
			parsed := fmt.Sprintf("解析器%d处理的数据: %s", parserID, string(rawData))
			
			select {
			case s.parsedChan <- parsed:
				// 成功发送到解析通道
			case <-time.After(time.Duration(s.dataProcessConfig.FlushDelayMs) * time.Millisecond):
				// 超时，发送到死信队列
				select {
				case s.deadChan <- rawData:
				default:
					log.Printf("死信队列已满，丢弃数据")
				}
			}
			
		case <-s.stopChan:
			log.Printf("数据解析器 %d 已停止", parserID)
			return
		}
	}
}

// dbWriter 数据库写入器
func (s *ConfigAwareService) dbWriter(writerID int) {
	defer s.wg.Done()
	
	log.Printf("数据库写入器 %d 已启动", writerID)
	
	batch := make([]interface{}, 0, s.dataProcessConfig.BatchSize)
	ticker := time.NewTicker(time.Duration(s.dataProcessConfig.FlushDelayMs) * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case data := <-s.parsedChan:
			batch = append(batch, data)
			
			// 批量写入
			if len(batch) >= s.dataProcessConfig.BatchSize {
				s.flushBatch(batch, writerID)
				batch = batch[:0] // 清空批次
			}
			
		case <-ticker.C:
			// 定时刷新
			if len(batch) > 0 {
				s.flushBatch(batch, writerID)
				batch = batch[:0]
			}
			
		case <-s.stopChan:
			// 停止前刷新剩余数据
			if len(batch) > 0 {
				s.flushBatch(batch, writerID)
			}
			log.Printf("数据库写入器 %d 已停止", writerID)
			return
		}
	}
}

// flushBatch 批量写入数据库
func (s *ConfigAwareService) flushBatch(batch []interface{}, writerID int) {
	log.Printf("写入器 %d 批量写入 %d 条数据", writerID, len(batch))
	// 这里实现实际的数据库写入逻辑
}

// startCleanupTask 启动清理任务
func (s *ConfigAwareService) startCleanupTask() {
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		
		ticker := time.NewTicker(time.Duration(s.dataProcessConfig.CleanupIntervalHours) * time.Hour)
		defer ticker.Stop()
		
		log.Printf("清理任务已启动，间隔: %d小时", s.dataProcessConfig.CleanupIntervalHours)
		
		for {
			select {
			case <-ticker.C:
				s.performCleanup()
			case <-s.stopChan:
				log.Println("清理任务已停止")
				return
			}
		}
	}()
}

// performCleanup 执行清理操作
func (s *ConfigAwareService) performCleanup() {
	log.Printf("执行数据清理，保留 %d 天的数据", s.dataProcessConfig.DataRetentionDays)
	
	// 清理过期数据
	cutoffTime := time.Now().AddDate(0, 0, -s.dataProcessConfig.DataRetentionDays)
	log.Printf("删除 %s 之前的数据", cutoffTime.Format("2006-01-02"))
	
	// 这里实现实际的数据清理逻辑
	// result := s.db.Where("created_at < ?", cutoffTime).Delete(&model.BondQuote{})
	
	log.Println("✓ 数据清理完成")
}
