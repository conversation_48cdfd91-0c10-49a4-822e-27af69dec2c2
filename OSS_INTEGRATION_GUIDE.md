# OSS集成指南

## 概述

已为你的Excel导出功能添加了OSS上传的抽象接口，**完全兼容现有代码**，不会影响任何现有功能。你可以选择性地使用OSS功能。

## 新增功能

### 1. 抽象OSS上传函数

```go
// UploadToOSS 上传文件到OSS
func (s *BondQueryService) UploadToOSS(localFilePath string) (string, error)
```

**特点：**
- 支持阿里云OSS、腾讯云COS、AWS S3等
- 自动生成OSS路径：`bond-exports/2025/01/02/filename.xlsx`
- 返回OSS访问URL
- 包含详细的实现示例代码

### 2. 便捷组合函数

```go
// ExportAndUploadToOSS 导出并上传到OSS的便捷方法
func (s *BondQueryService) ExportAndUploadToOSS(exportFunc func() (string, error)) (map[string]string, error)
```

**返回结果：**
```json
{
  "local_file": "bond_latest_quotes_20250711_184530.xlsx",
  "oss_url": "https://your-oss-domain.com/bond-exports/2025/07/11/bond_latest_quotes_20250711_184530.xlsx"
}
```

## 新增API接口

### 原有接口（保持不变）
- `GET /api/bond/query/current-latest` - 导出到本地
- `GET /api/bond/query/daily-end` - 导出到本地
- `GET /api/bond/query/time-range` - 导出到本地

### 新增OSS接口
- `GET /api/bond/query/current-latest-oss` - 导出并上传OSS
- `GET /api/bond/query/daily-end-oss` - 导出并上传OSS
- `GET /api/bond/query/time-range-oss` - 导出并上传OSS

## 使用方式

### 1. 仅导出到本地（原有功能）
```bash
curl "http://localhost:8081/api/bond/query/current-latest"
```

### 2. 导出并上传到OSS（新功能）
```bash
curl "http://localhost:8081/api/bond/query/current-latest-oss"
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "导出并上传OSS成功",
  "data": {
    "local_file": "bond_latest_quotes_20250711_184530.xlsx",
    "oss_url": "https://your-oss-domain.com/bond-exports/2025/07/11/bond_latest_quotes_20250711_184530.xlsx"
  }
}
```

## OSS实现指南

### 1. 阿里云OSS实现

在 `UploadToOSS` 函数中取消注释并修改：

```go
import "github.com/aliyun/aliyun-oss-go-sdk/oss"

// 在UploadToOSS函数中实现
client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
if err != nil {
    return "", err
}
bucket, err := client.Bucket(bucketName)
if err != nil {
    return "", err
}
err = bucket.PutObject(ossPath, bytes.NewReader(fileContent))
if err != nil {
    return "", err
}

// 生成访问URL
ossURL := fmt.Sprintf("https://%s.%s/%s", bucketName, endpoint, ossPath)
```

### 2. 腾讯云COS实现

```go
import "github.com/tencentyun/cos-go-sdk-v5"

client := cos.NewClient(baseURL, &http.Client{
    Transport: &cos.AuthorizationTransport{
        SecretID:  secretID,
        SecretKey: secretKey,
    },
})
_, err = client.Object.Put(context.Background(), ossPath, bytes.NewReader(fileContent), nil)
```

### 3. AWS S3实现

```go
import "github.com/aws/aws-sdk-go/aws"
import "github.com/aws/aws-sdk-go/service/s3"

sess, err := session.NewSession(&aws.Config{
    Region: aws.String(region),
    Credentials: credentials.NewStaticCredentials(accessKey, secretKey, ""),
})
svc := s3.New(sess)
_, err = svc.PutObject(&s3.PutObjectInput{
    Bucket: aws.String(bucketName),
    Key:    aws.String(ossPath),
    Body:   bytes.NewReader(fileContent),
})
```

## 配置建议

可以在 `config.yaml` 中添加OSS配置：

```yaml
oss:
  provider: "aliyun"  # aliyun, tencent, aws
  endpoint: "oss-cn-hangzhou.aliyuncs.com"
  accessKeyId: "your-access-key"
  accessKeySecret: "your-secret-key"
  bucketName: "your-bucket"
  domain: "https://your-custom-domain.com"  # 可选的自定义域名
```

然后在配置结构体中添加对应字段。

## 优势特点

1. **完全兼容**：不影响现有任何功能
2. **灵活选择**：可以选择仅导出本地或同时上传OSS
3. **多云支持**：支持主流云服务商
4. **自动路径**：按日期自动组织OSS文件路径
5. **错误处理**：完善的错误处理和状态返回
6. **易于扩展**：抽象设计便于后续功能扩展

## 实施步骤

1. **选择OSS服务商**（阿里云、腾讯云、AWS等）
2. **安装对应SDK**：`go get github.com/aliyun/aliyun-oss-go-sdk/oss`
3. **实现UploadToOSS函数**：根据上面的示例代码
4. **配置OSS参数**：在配置文件中添加OSS配置
5. **测试功能**：使用新的OSS接口测试上传

现在你的系统既保持了原有的本地导出功能，又具备了OSS上传能力，可以根据需要灵活选择使用方式！
