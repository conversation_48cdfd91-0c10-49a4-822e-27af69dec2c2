# 债券行情Excel导出API文档

## 概述

`query_service.go` 已经改造为Excel导出服务，提供了三种不同的导出功能：

1. **导出当前最新行情** - 导出数据库中当前所有债券的最新行情
2. **导出日终数据** - 导出指定日期范围的日终行情数据
3. **导出时间段数据** - 导出指定日期和时间段的明细行情数据

## API接口

### 1. 导出当前最新行情

**接口地址：** `GET /api/bond/query/current-latest`

**描述：** 导出数据库中当前所有债券的最新行情到Excel文件

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "msg": "导出成功",
  "data": {
    "filename": "bond_latest_quotes_20250711_184530.xlsx"
  }
}
```

### 2. 导出日终数据

**接口地址：** `GET /api/bond/query/daily-end`

**描述：** 导出指定日期范围的日终行情数据

**请求参数：**
- `startDate` (string): 开始日期，格式：YYYYMMDD，如：20250708
- `endDate` (string): 结束日期，格式：YYYYMMDD，如：20250708

**请求示例：**
```
GET /api/bond/query/daily-end?startDate=20250708&endDate=20250708
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "导出成功",
  "data": {
    "filename": "bond_daily_end_data_20250708_to_20250708.xlsx"
  }
}
```

### 3. 导出时间段数据

**接口地址：** `GET /api/bond/query/time-range`

**描述：** 导出指定日期和时间段的明细行情数据

**请求参数：**
- `date` (string): 日期，格式：YYYYMMDD，如：20250708
- `startTime` (string): 开始时间，格式：HH:MM:SS，如：09:00:00
- `endTime` (string): 结束时间，格式：HH:MM:SS，如：17:00:00

**请求示例：**
```
GET /api/bond/query/time-range?date=20250708&startTime=09:00:00&endTime=17:00:00
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "导出成功",
  "data": {
    "filename": "bond_time_range_data_20250708_09:00:00_to_17:00:00.xlsx"
  }
}
```

### 4. 下载文件

**接口地址：** `GET /api/bond/query/download/:filename`

**描述：** 下载生成的Excel文件

**请求示例：**
```
GET /api/bond/query/download/bond_latest_quotes_20250711_184530.xlsx
```

## Excel文件格式

### 最新行情和日终数据格式

| 列名 | 说明 |
|------|------|
| 债券代码 | 债券ISIN代码 |
| 买方价格 | 买方报价 |
| 买方收益率 | 买方收益率 |
| 买方数量 | 买方报价数量 |
| 买方报价时间 | 买方报价时间 |
| 卖方价格 | 卖方报价 |
| 卖方收益率 | 卖方收益率 |
| 卖方数量 | 卖方报价数量 |
| 卖方报价时间 | 卖方报价时间 |
| 消息ID | 消息标识 |
| 消息类型 | 消息类型 |
| 发送时间 | 消息发送时间 |
| 时间戳 | 业务时间戳 |
| 更新时间 | 最后更新时间 |
| 买方券商ID | 买方券商标识 |
| 卖方券商ID | 卖方券商标识 |

### 时间段明细数据格式

| 列名 | 说明 |
|------|------|
| 债券代码 | 债券ISIN代码 |
| 报价方向 | BID/ASK |
| 价格 | 报价 |
| 收益率 | 收益率 |
| 数量 | 报价数量 |
| 报价时间 | 报价时间 |
| 券商ID | 券商标识 |
| 消息ID | 消息标识 |
| 消息类型 | 消息类型 |
| 发送时间 | 消息发送时间 |
| 时间戳 | 业务时间戳 |

## 使用说明

1. **数据库兼容性**：服务已修复SQLite兼容性问题，支持SQLite和MySQL
2. **文件存储**：Excel文件默认保存在项目根目录
3. **错误处理**：所有接口都有完善的错误处理和参数验证
4. **性能优化**：使用了对象池来优化JSON解析性能

## 测试方法

可以使用curl或Postman等工具测试接口：

```bash
# 导出当前最新行情
curl "http://localhost:8081/api/bond/query/current-latest"

# 导出日终数据
curl "http://localhost:8081/api/bond/query/daily-end?startDate=20250708&endDate=20250708"

# 导出时间段数据
curl "http://localhost:8081/api/bond/query/time-range?date=20250708&startTime=09:00:00&endTime=17:00:00"
```
